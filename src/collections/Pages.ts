import { access } from "@/access";
import { genPreviewPath } from "@/utils/gen-preview-path";
import type { CollectionConfig } from "payload";
import {
	MetaDescriptionField,
	MetaImageField,
	MetaTitleField,
	OverviewField,
	PreviewField,
} from "@payloadcms/plugin-seo/fields";
import { slugField } from "@/fields/slug";
import { baseBlocks } from "@/blocks";
import {
	FixedToolbarFeature,
	HeadingFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";

export const Pages: CollectionConfig = {
	slug: "pages",
	access: {
		create: access.authenticated,
		delete: access.authenticated,
		read: access.authenticatedOrPublished,
		update: access.authenticated,
	},
	defaultPopulate: {
		title: true,
		slug: true,
	},
	admin: {
		defaultColumns: ["title", "slug", "updatedAt"],
		livePreview: {
			url: ({ data }) =>
				genPreviewPath({
					slug: typeof data?.slug === "string" ? data.slug : "",
					collection: "pages",
				}),
		},
		preview: (data) =>
			genPreviewPath({
				slug: typeof data?.slug === "string" ? data.slug : "",
				collection: "pages",
			}),
		useAsTitle: "title",
	},
	fields: [
		{
			name: "title",
			type: "text",
			required: true,
		},
		{
			type: "tabs",
			tabs: [
				{
					label: "Hero",
					fields: [
						{
							name: "hero",
							type: "group",
							fields: [
								{
									name: "type",
									type: "select",
									defaultValue: "low-impact",
									label: "Type",
									options: [
										{
											label: "None",
											value: "none",
										},
										{
											label: "High Impact",
											value: "high-impact",
										},
										{
											label: "Low Impact",
											value: "low-impact",
										},
										{
											label: "About Hero",
											value: "about-hero",
										},
										{
											label: "Concert Hero",
											value: "concert-hero",
										},
									],
									required: true,
								},
								{
									name: "title",
									type: "text",
									admin: {
										condition: (_, siblingData) =>
											Boolean(
												[
													"high-impact",
													"low-impact",
													"concert-hero",
													"about-hero",
												].includes(siblingData?.type),
											),
									},
									required: true,
									label: "Titel",
								},
								{
									name: "content",
									type: "text",
									admin: {
										condition: (_, siblingData) =>
											Boolean(
												[
													"high-impact",
													"low-impact",
													"concert-hero",
													"about-hero",
												].includes(siblingData?.type),
											),
									},
									label: "Text",
								},
								{
									name: "richText",
									type: "richText",
									editor: lexicalEditor({
										features: ({ rootFeatures }) => {
											return [
												...rootFeatures,
												HeadingFeature({
													enabledHeadingSizes: ["h2", "h3", "h4"],
												}),
												FixedToolbarFeature(),
												InlineToolbarFeature(),
												FontColorFeature(),
											];
										},
									}),
									admin: {
										condition: (_, siblingData) =>
											Boolean(["about-hero"].includes(siblingData?.type)),
									},
								},
								{
									type: "row",
									fields: [
										{
											name: "image",
											type: "upload",
											relationTo: "media",
											admin: {
												condition: (_, siblingData) =>
													Boolean(
														["high-impact", "low-impact"].includes(
															siblingData?.type,
														),
													),
												width: "50%",
											},
											required: true,
											hasMany: false,
											label: "Hintergrundbild",
										},
										{
											name: "variant",
											type: "select",
											admin: {
												condition: (_, siblingData) =>
													Boolean(["low-impact"].includes(siblingData?.type)),
												width: "50%",
											},
											label: "Hintergrund Variante",
											options: [
												{
													label: "Standard",
													value: "standard",
												},
												{
													label: "Gedimmt",
													value: "dimmed",
												},
												{
													label: "Multipliziert",
													value: "multiplied",
												},
											],
										},
									],
								},
							],
						},
					],
				},
				{
					label: "Content",
					fields: [
						{
							name: "layout",
							type: "blocks",
							blocks: baseBlocks,
							required: true,
						},
					],
				},
				{
					name: "meta",
					label: "SEO",
					fields: [
						OverviewField({
							titlePath: "meta.title",
							descriptionPath: "meta.description",
							imagePath: "meta.image",
						}),
						MetaTitleField({ hasGenerateFn: true }),
						MetaImageField({ relationTo: "media" }),
						MetaDescriptionField({}),
						PreviewField({
							hasGenerateFn: true,
							titlePath: "meta.title",
							descriptionPath: "meta.description",
						}),
					],
				},
			],
		},
		{
			name: "publishedAt",
			type: "date",
			admin: {
				position: "sidebar",
			},
		},
		...slugField(),
	],
	versions: {
		drafts: {
			autosave: {
				interval: 100,
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
