import { link } from "@/fields/link";
import { FontColorFeature } from "@/lexical-features/font-color/font-color-feature.server";
import {
	FixedToolbarFeature,
	InlineToolbarFeature,
	lexicalEditor,
} from "@payloadcms/richtext-lexical";
import type { Block } from "payload";

export const ChoirDirectorBlockConfig: Block = {
	slug: "choir-director",
	interfaceName: "ChoirDirectorBlock",
	fields: [
		{
			name: "richText",
			type: "richText",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => {
					return [
						...rootFeatures,
						FixedToolbarFeature(),
						InlineToolbarFeature(),
						FontColorFeature(),
					];
				},
			}),
			label: false,
		},
		{
			name: "images",
			type: "upload",
			hasMany: true,
			relationTo: "media",
			admin: {
				description:
					"Bilder werden für die 'Galerie' verwendet. Jedes Bild wird 1 sekunden lang angezeigt.",
			},
		},
		link({ appearances: false }),
		{
			name: "overlapWithNext",
			type: "checkbox",
			label:
				"Überlappen mit dem nächsten Block (macht sinn wenn der nächste Block gleich der Footer ist)",
		},
	],
};
